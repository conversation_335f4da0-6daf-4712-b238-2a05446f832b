'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import Script from 'next/script';
import Head from 'next/head';

interface LinkData {
  originalUrl: string;
  clicks: number;
  createdAt: string;
}

export default function BlogPage() {
  const params = useParams();
  const router = useRouter();
  const shortCode = params.shortCode as string;
  
  const [, setLinkData] = useState<LinkData | null>(null);
  const [loading] = useState(false); // Start with false to prevent flash
  const [] = useState<string | null>(null);
  const [initialCountdown, setInitialCountdown] = useState(10); // 10 second initial countdown
  const [showFirstContinue, setShowFirstContinue] = useState(false); // Show first continue button
  const [secondCountdown, setSecondCountdown] = useState(20); // 20 second second countdown
  const [showFinalContinue, setShowFinalContinue] = useState(false); // Show final continue button
  const [secondPhase, setSecondPhase] = useState(false); // Track if in second phase

  // Fetch link data in background - don't block the page
  useEffect(() => {
    // Set default data immediately
    setLinkData({
      originalUrl: 'https://example.com',
      clicks: 0,
      createdAt: new Date().toISOString()
    });

    // Try to fetch real data in background
    const fetchLinkData = async () => {
      try {
        const response = await fetch(`/api/redirect/${shortCode}`);
        const data = await response.json();

        if (response.ok) {
          setLinkData(data);
        }
      } catch {
        console.log('Failed to fetch link data, using default');
      }
    };

    fetchLinkData();
  }, [shortCode]);

  // Phase 1: 10-second initial countdown
  useEffect(() => {
    if (!loading && !error && initialCountdown > 0 && !secondPhase) {
      const timer = setInterval(() => {
        setInitialCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            setShowFirstContinue(true);
            console.log('Blog page: 10 seconds completed, showing first continue button');
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [loading, error, initialCountdown, secondPhase]);

  // Phase 2: 20-second countdown after first continue clicked
  useEffect(() => {
    if (secondPhase && secondCountdown > 0) {
      const timer = setInterval(() => {
        setSecondCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            setShowFinalContinue(true);
            console.log('Blog page: 20 seconds completed, showing final continue button');
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [secondPhase, secondCountdown]);

  // Monitor for Montag ads loading
  useEffect(() => {
    if (!loading && !error) {
      const checkAds = setInterval(() => {
        // Check for Montag ad elements
        const adElements = document.querySelectorAll('[class*="ad"], [id*="ad"], iframe');
        const montagScripts = document.querySelectorAll('script[src*="fpyf8"]');
        const socialAds = document.querySelectorAll('[class*="social"], [class*="montag"]');

        console.log('Page 1 - Montag ads monitoring:', {
          adElements: adElements.length,
          montagScripts: montagScripts.length,
          socialAds: socialAds.length,
          bodyChildren: document.body.children.length
        });
      }, 8000); // Check every 8 seconds

      return () => clearInterval(checkAds);
    }
  }, [loading, error]);

  const handleFirstContinue = () => {
    console.log('Blog page: Starting second phase (20-second countdown)');
    setSecondPhase(true);
    setShowFirstContinue(false);
  };

  const handleFinalContinue = () => {
    console.log('Blog page: Navigating to quiz page');
    router.push(`/s/${shortCode}/verify-challenge-x9k2m`);
  };

  // Show minimal loading, no error screen to prevent flash
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-400 mx-auto mb-4"></div>
          <p className="text-white text-xl">Loading...</p>
        </div>
      </div>
    );
  }

  // Don't show error screen - just continue with default data

  return (
    <>
      <Head>
        <title>Gaming Blog - GameHub Link</title>
      </Head>
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        {/* Header */}
        <nav className="bg-black/20 backdrop-blur-sm border-b border-purple-500/20 p-4">
          <div className="max-w-4xl mx-auto flex flex-col sm:flex-row justify-between items-center gap-2">
            <Link href="/" className="text-xl sm:text-2xl font-bold text-white hover:opacity-80 transition-opacity cursor-pointer">
              🎮 <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">GameHub</span>
            </Link>
            <div className="text-purple-400 font-mono text-sm sm:text-base">
              Gaming Blog - Step 1 of 3
            </div>
          </div>
        </nav>

        <div className="max-w-4xl mx-auto p-4 sm:p-6">
          {/* Phase 1: Initial 10-second countdown */}
          {!secondPhase && initialCountdown > 0 && (
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 sm:p-8 mb-6 text-center">
              <h2 className="text-xl sm:text-2xl font-bold text-white mb-4 sm:mb-6">
                🎮 Preparing Your Gaming Content
              </h2>
              <div className="bg-purple-600 text-white rounded-full w-20 h-20 sm:w-24 sm:h-24 flex items-center justify-center mx-auto mb-4 sm:mb-6 text-2xl sm:text-3xl font-bold">
                {initialCountdown}
              </div>
              <p className="text-purple-300 mb-4 text-sm sm:text-base">
                ⏳ Please wait {initialCountdown} seconds while we load your content...
              </p>
              <div className="flex items-center justify-center space-x-2">
                <div className="w-3 h-3 bg-purple-400 rounded-full animate-bounce"></div>
                <div className="w-3 h-3 bg-pink-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                <div className="w-3 h-3 bg-purple-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
              </div>
            </div>
          )}

          {/* Phase 1.5: First continue button */}
          {!secondPhase && showFirstContinue && (
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 mb-6 text-center">
              <h2 className="text-2xl font-bold text-white mb-6">
                ✅ Content Ready!
              </h2>
              <p className="text-gray-300 mb-6">
                Your gaming content is now ready. Click continue to proceed.
              </p>
              <button
                onClick={handleFirstContinue}
                className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-all transform hover:scale-105"
              >
                Continue →
              </button>
            </div>
          )}

          {/* Phase 2: Second countdown + scroll instruction */}
          {secondPhase && secondCountdown > 0 && (
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 mb-6 text-center">
              <h2 className="text-2xl font-bold text-white mb-6">
                📖 Loading Gaming Blog Content
              </h2>
              <div className="bg-green-600 text-white rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6 text-3xl font-bold">
                {secondCountdown}
              </div>
              <p className="text-green-300 mb-4">
                ⏳ Please wait {secondCountdown} seconds while content loads...
              </p>
              <p className="text-yellow-300 text-lg font-semibold mb-4">
                👇 Scroll down to read the gaming blog while you wait!
              </p>
              <div className="flex items-center justify-center space-x-2">
                <div className="w-3 h-3 bg-green-400 rounded-full animate-bounce"></div>
                <div className="w-3 h-3 bg-yellow-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                <div className="w-3 h-3 bg-green-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
              </div>
            </div>
          )}



          {/* Blog Content - Only show during second phase */}
          {secondPhase && (
            <article className="bg-white/10 backdrop-blur-sm rounded-lg p-8 mb-6">
            <h1 className="text-3xl font-bold text-white mb-6">
              🎮 The Ultimate Guide to Gaming in 2024
            </h1>
            
            <div className="prose prose-invert max-w-none">
              <p className="text-gray-300 mb-4 leading-relaxed">
                Gaming has evolved tremendously over the past decade, and 2024 promises to be one of the most exciting years yet for gamers worldwide.
                From cutting-edge graphics to immersive virtual reality experiences, the gaming industry continues to push boundaries and redefine entertainment.
              </p>

              <h2 className="text-2xl font-semibold text-purple-300 mb-4 mt-8">🚀 Latest Gaming Trends</h2>
              <p className="text-gray-300 mb-4 leading-relaxed">
                The rise of cloud gaming has made high-quality gaming accessible to more people than ever before. Services like Xbox Game Pass,
                PlayStation Now, and Google Stadia have revolutionized how we access and play games. No longer do you need expensive hardware
                to enjoy the latest AAA titles.
              </p>



              <h2 className="text-2xl font-semibold text-purple-300 mb-4">🎯 Best Gaming Platforms</h2>
              <p className="text-gray-300 mb-4 leading-relaxed">
                Whether you&apos;re a PC master race enthusiast or a console loyalist, there&apos;s never been a better time to be a gamer.
                The PlayStation 5 and Xbox Series X/S have brought 4K gaming and ray tracing to the mainstream, while the Nintendo Switch
                continues to dominate the portable gaming market.
              </p>

              <ul className="text-gray-300 mb-6 space-y-2">
                <li>• <strong className="text-purple-300">PC Gaming:</strong> Ultimate customization and performance</li>
                <li>• <strong className="text-purple-300">PlayStation 5:</strong> Exclusive titles and innovative DualSense controller</li>
                <li>• <strong className="text-purple-300">Xbox Series X/S:</strong> Game Pass integration and backward compatibility</li>
                <li>• <strong className="text-purple-300">Nintendo Switch:</strong> Portable gaming perfection</li>
              </ul>

              <h2 className="text-2xl font-semibold text-purple-300 mb-4">🏆 Top Games to Play Right Now</h2>
              <p className="text-gray-300 mb-4 leading-relaxed">
                The gaming landscape is filled with incredible titles across all genres. From epic single-player adventures to competitive 
                multiplayer experiences, there's something for every type of gamer.
              </p>



              <p className="text-gray-300 mb-4 leading-relaxed">
                Action-adventure games like &quot;The Legend of Zelda: Tears of the Kingdom&quot; and &quot;Spider-Man 2&quot; have set new standards
                for open-world exploration and storytelling. Meanwhile, competitive games like &quot;Valorant&quot; and &quot;Apex Legends&quot;
                continue to evolve with regular updates and seasonal content.
              </p>

              <h2 className="text-2xl font-semibold text-purple-300 mb-4">💡 Gaming Tips for Beginners</h2>
              <p className="text-gray-300 mb-4 leading-relaxed">
                Starting your gaming journey can be overwhelming with so many options available. Here are some essential tips 
                to help you get started and make the most of your gaming experience.
              </p>

              <div className="bg-purple-900/20 rounded-lg p-6 mb-6">
                <h3 className="text-xl font-semibold text-purple-300 mb-3">Essential Gaming Setup</h3>
                <ul className="text-gray-300 space-y-2">
                  <li>• Invest in a comfortable gaming chair for long sessions</li>
                  <li>• Good headphones or speakers for immersive audio</li>
                  <li>• Stable internet connection for online gaming</li>
                  <li>• Proper lighting to reduce eye strain</li>
                </ul>
              </div>



              <h2 className="text-2xl font-semibold text-purple-300 mb-4">🌟 The Future of Gaming</h2>
              <p className="text-gray-300 mb-4 leading-relaxed">
                As we look ahead, the future of gaming is incredibly bright. Virtual reality is becoming more accessible, 
                artificial intelligence is creating more dynamic game worlds, and cross-platform play is bringing gamers together 
                regardless of their chosen platform.
              </p>

              <p className="text-gray-300 mb-8 leading-relaxed">
                The integration of blockchain technology and NFTs in gaming is also creating new opportunities for players 
                to truly own their in-game assets. While still in its early stages, this technology could revolutionize 
                how we think about digital ownership in games.
              </p>
            </div>
          </article>
          )}

          {/* Final Continue Button - Only shows after second countdown */}
          {secondPhase && showFinalContinue && (
            <div className="text-center mb-8">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 mb-4">
                <h3 className="text-xl font-bold text-green-400 mb-4">🎉 Ready to Continue!</h3>
                <p className="text-gray-300 mb-4">
                  You&apos;ve read the gaming content. Now proceed to the next challenge!
                </p>
              </div>
              <button
                onClick={handleFinalContinue}
                className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-12 py-4 rounded-lg font-semibold hover:from-green-700 hover:to-emerald-700 transition-all transform hover:scale-105 text-lg"
              >
                Continue to Math Quiz →
              </button>
            </div>
          )}
        </div>

        {/* Montag Multitag Ad Network Script */}
        <Script
          src="https://fpyf8.com/88/tag.min.js"
          data-zone="156349"
          async
          data-cfasync="false"
          strategy="afterInteractive"
          onLoad={() => console.log('Page 1: Montag multitag script loaded')}
          onError={() => console.log('Page 1: Montag multitag script failed')}
        />
      </div>
    </>
  );
}
