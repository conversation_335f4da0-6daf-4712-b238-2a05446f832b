import { Pool } from 'pg';

// Database connection pool
let pool: Pool | null = null;

export function getPool(): Pool {
  if (!pool) {
    pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
    });
  }
  return pool;
}

// Initialize database tables
export async function initDatabase() {
  const client = getPool();
  
  try {
    // Create shortened_links table
    await client.query(`
      CREATE TABLE IF NOT EXISTS shortened_links (
        id SERIAL PRIMARY KEY,
        short_code VARCHAR(10) UNIQUE NOT NULL,
        original_url TEXT NOT NULL,
        clicks INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create index for faster lookups
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_short_code ON shortened_links(short_code)
    `);

    console.log('Database initialized successfully');
  } catch (error) {
    console.error('Database initialization error:', error);
    throw error;
  }
}

// URL operations
export interface UrlRecord {
  id: number;
  short_code: string;
  original_url: string;
  clicks: number;
  created_at: Date;
  updated_at: Date;
}

export async function createShortUrl(shortCode: string, originalUrl: string): Promise<UrlRecord> {
  const client = getPool();
  
  const result = await client.query(
    'INSERT INTO shortened_links (short_code, original_url) VALUES ($1, $2) RETURNING *',
    [shortCode, originalUrl]
  );
  
  return result.rows[0];
}

export async function getUrlByShortCode(shortCode: string): Promise<UrlRecord | null> {
  const client = getPool();
  
  const result = await client.query(
    'SELECT * FROM shortened_links WHERE short_code = $1',
    [shortCode]
  );
  
  return result.rows[0] || null;
}

export async function incrementClicks(shortCode: string): Promise<number> {
  const client = getPool();
  
  const result = await client.query(
    'UPDATE shortened_links SET clicks = clicks + 1, updated_at = CURRENT_TIMESTAMP WHERE short_code = $1 RETURNING clicks',
    [shortCode]
  );
  
  return result.rows[0]?.clicks || 0;
}

export async function getRecentLinks(limit: number = 10): Promise<UrlRecord[]> {
  const client = getPool();
  
  const result = await client.query(
    'SELECT * FROM shortened_links ORDER BY created_at DESC LIMIT $1',
    [limit]
  );
  
  return result.rows;
}

export async function checkShortCodeExists(shortCode: string): Promise<boolean> {
  const client = getPool();
  
  const result = await client.query(
    'SELECT 1 FROM shortened_links WHERE short_code = $1',
    [shortCode]
  );
  
  return result.rows.length > 0;
}
