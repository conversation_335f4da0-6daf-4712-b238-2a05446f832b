@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Custom utilities for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Hide Next.js development indicator and circular N logo */
#__next-build-watcher,
[data-nextjs-scroll-focus-boundary],
.__next-dev-overlay,
#__next-dev-overlay,
.__next-dev-overlay-backdrop,
#__next-dev-overlay-backdrop,
[data-nextjs-dialog],
[data-nextjs-dialog-overlay],
.__next-dev-overlay-left,
.__next-dev-overlay-right,
.__next-dev-overlay-top,
.__next-dev-overlay-bottom,
[data-nextjs-toast],
[data-nextjs-toast-wrapper] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

/* Hide the circular N logo specifically */
div[style*="position: fixed"][style*="bottom: 16px"][style*="left: 16px"],
div[style*="position: fixed"][style*="bottom: 1rem"][style*="left: 1rem"],
div[style*="position:fixed"][style*="bottom:16px"][style*="left:16px"],
div[style*="position:fixed"][style*="bottom:1rem"][style*="left:1rem"] {
  display: none !important;
}

/* Hide any development overlay elements */
body > div[style*="position: fixed"]:not([data-allowed]) {
  display: none !important;
}

/* Additional security - hide development tools */
.__next > div[style*="position: fixed"],
#__next > div[style*="position: fixed"] {
  display: none !important;
}
