import { NextResponse } from 'next/server';
import { urlStore } from '@/lib/urlStore';

export async function GET() {
  try {
    // Get all links from store
    const allLinks = await urlStore.getAll();
    
    return NextResponse.json({
      success: true,
      totalLinks: allLinks.length,
      links: allLinks,
      storeType: process.env.DATABASE_URL ? 'database' : 'memory'
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      storeType: process.env.DATABASE_URL ? 'database' : 'memory'
    });
  }
}
