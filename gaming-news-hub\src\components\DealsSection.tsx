'use client';

import { useState, useEffect } from 'react';
import { Deal } from '@/lib/dealsParser';

interface DealCardProps {
  deal: Deal;
}

function DealCard({ deal }: DealCardProps) {
  const [imageError, setImageError] = useState(false);
  
  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 hover:bg-white/20 transition-all transform hover:scale-105">
      {/* Product Image/Thumbnail */}
      <div className="h-32 rounded-lg mb-4 overflow-hidden">
        {!imageError && deal.thumbnail.url ? (
          // eslint-disable-next-line @next/next/no-img-element
          <img
            src={deal.thumbnail.url}
            alt={deal.thumbnail.alt}
            className="w-full h-full object-cover"
            onError={handleImageError}
          />
        ) : (
          <div className={`w-full h-full bg-gradient-to-br ${deal.thumbnail.fallback} flex items-center justify-center`}>
            <span className="text-white text-2xl font-semibold">
              {deal.emoji} {deal.title.split(' ').slice(0, 2).join(' ')}
            </span>
          </div>
        )}
      </div>

      {/* Product Info */}
      <h4 className="text-lg font-semibold text-white mb-2 line-clamp-2">
        {deal.title}
      </h4>
      
      {/* Pricing */}
      <div className="flex items-center gap-2 mb-2">
        <span className="text-2xl font-bold text-green-400">
          ${deal.salePrice.toFixed(2)}
        </span>
        <span className="text-gray-400 line-through">
          ${deal.originalPrice.toFixed(2)}
        </span>
      </div>
      
      {/* Discount Badge */}
      <div className="flex items-center justify-between">
        <span className="bg-red-500 text-white px-2 py-1 rounded text-sm font-semibold">
          {deal.discountPercentage}% OFF
        </span>
        
        {/* Buy Button */}
        <a
          href={deal.productUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-2 rounded text-sm font-semibold hover:from-purple-700 hover:to-pink-700 transition-all"
        >
          Buy Now
        </a>
      </div>
    </div>
  );
}

interface DealsSectionProps {
  className?: string;
  id?: string;
}

export default function DealsSection({ className = '', id }: DealsSectionProps) {
  const [deals, setDeals] = useState<Deal[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDeals();
  }, []);

  const fetchDeals = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/deals');
      
      if (!response.ok) {
        throw new Error('Failed to fetch deals');
      }
      
      const data = await response.json();
      setDeals(data.deals || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load deals');
      console.error('Error fetching deals:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <section id={id} className={`py-16 px-4 sm:px-6 lg:px-8 bg-black/20 ${className}`}>
        <div className="max-w-7xl mx-auto">
          <h3 className="text-3xl font-bold text-white mb-8 text-center">Hot Gaming Deals</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="bg-white/10 backdrop-blur-sm rounded-lg p-6 animate-pulse">
                <div className="h-32 bg-gray-600 rounded-lg mb-4"></div>
                <div className="h-4 bg-gray-600 rounded mb-2"></div>
                <div className="h-6 bg-gray-600 rounded mb-2 w-3/4"></div>
                <div className="h-4 bg-gray-600 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section id={id} className={`py-16 px-4 sm:px-6 lg:px-8 bg-black/20 ${className}`}>
        <div className="max-w-7xl mx-auto text-center">
          <h3 className="text-3xl font-bold text-white mb-8">Hot Gaming Deals</h3>
          <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-6">
            <p className="text-red-300 mb-4">Failed to load deals: {error}</p>
            <button
              onClick={fetchDeals}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </section>
    );
  }

  if (deals.length === 0) {
    return (
      <section id={id} className={`py-16 px-4 sm:px-6 lg:px-8 bg-black/20 ${className}`}>
        <div className="max-w-7xl mx-auto text-center">
          <h3 className="text-3xl font-bold text-white mb-8">Hot Gaming Deals</h3>
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8">
            <div className="text-center">
              <div className="text-6xl mb-4">🛍️</div>
              <h4 className="text-xl font-semibold text-white mb-2">No Gaming Deals Yet</h4>
              <p className="text-gray-300 mb-4">
                Add gaming deals through environment variables to see them here.
              </p>
              <div className="bg-purple-500/20 border border-purple-500/30 rounded-lg p-4 text-left">
                <p className="text-purple-300 text-sm font-mono">
                  DEAL_1=Product Title|Original Price|Sale Price|Product URL
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id={id} className={`py-16 px-4 sm:px-6 lg:px-8 bg-black/20 ${className}`}>
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h3 className="text-3xl font-bold text-white">Hot Gaming Deals</h3>
          <button
            onClick={fetchDeals}
            className="text-purple-400 hover:text-purple-300 text-sm transition-colors"
          >
            🔄 Refresh Deals
          </button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {deals.map((deal) => (
            <DealCard key={deal.id} deal={deal} />
          ))}
        </div>
        
        {deals.length > 0 && (
          <div className="text-center mt-8">
            <p className="text-gray-400 text-sm">
              Showing {deals.length} amazing deals • Updated automatically
            </p>
          </div>
        )}
      </div>
    </section>
  );
}
