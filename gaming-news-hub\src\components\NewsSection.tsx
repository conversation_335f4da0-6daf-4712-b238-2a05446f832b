'use client';

import { useState, useEffect } from 'react';
import { NewsArticle } from '@/lib/newsParser';

interface NewsCardProps {
  article: NewsArticle;
}

function NewsCard({ article }: NewsCardProps) {
  const handleClick = () => {
    if (article.articleUrl) {
      window.open(article.articleUrl, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <div 
      className={`bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden hover:bg-white/20 transition-all transform hover:scale-105 ${
        article.articleUrl ? 'cursor-pointer' : ''
      }`}
      onClick={handleClick}
    >
      {/* News Image/Header */}
      <div className={`h-48 bg-gradient-to-br ${article.gradient} flex items-center justify-center`}>
        <span className="text-white text-lg font-semibold">
          {article.emoji} {article.category}
        </span>
      </div>
      
      {/* News Content */}
      <div className="p-6">
        <h4 className="text-xl font-semibold text-white mb-2 line-clamp-2">
          {article.title}
        </h4>
        <p className="text-gray-300 mb-4 line-clamp-3">
          {article.description}
        </p>
        <div className="flex items-center justify-between">
          <span className="text-purple-400 text-sm">
            {article.timeAgo}
          </span>
          {article.articleUrl && (
            <span className="text-purple-300 text-xs hover:text-purple-200 transition-colors">
              Read more →
            </span>
          )}
        </div>
      </div>
    </div>
  );
}

interface NewsSectionProps {
  className?: string;
  id?: string;
}

export default function NewsSection({ className = '', id }: NewsSectionProps) {
  const [news, setNews] = useState<NewsArticle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchNews();
  }, []);

  const fetchNews = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/news');
      
      if (!response.ok) {
        throw new Error('Failed to fetch news');
      }
      
      const data = await response.json();
      setNews(data.news || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load news');
      console.error('Error fetching news:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <section id={id} className={`py-16 px-4 sm:px-6 lg:px-8 ${className}`}>
        <div className="max-w-7xl mx-auto">
          <h3 className="text-3xl font-bold text-white mb-8 text-center">Latest Gaming News</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="bg-white/10 backdrop-blur-sm rounded-lg overflow-hidden animate-pulse">
                <div className="h-48 bg-gray-600"></div>
                <div className="p-6">
                  <div className="h-6 bg-gray-600 rounded mb-2"></div>
                  <div className="h-4 bg-gray-600 rounded mb-2"></div>
                  <div className="h-4 bg-gray-600 rounded mb-4 w-3/4"></div>
                  <div className="h-3 bg-gray-600 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section id={id} className={`py-16 px-4 sm:px-6 lg:px-8 ${className}`}>
        <div className="max-w-7xl mx-auto text-center">
          <h3 className="text-3xl font-bold text-white mb-8">Latest Gaming News</h3>
          <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-6">
            <p className="text-red-300 mb-4">Failed to load news: {error}</p>
            <button
              onClick={fetchNews}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </section>
    );
  }

  if (news.length === 0) {
    return (
      <section id={id} className={`py-16 px-4 sm:px-6 lg:px-8 ${className}`}>
        <div className="max-w-7xl mx-auto text-center">
          <h3 className="text-3xl font-bold text-white mb-8">Latest Gaming News</h3>
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8">
            <div className="text-center">
              <div className="text-6xl mb-4">📰</div>
              <h4 className="text-xl font-semibold text-white mb-2">No News Articles Yet</h4>
              <p className="text-gray-300 mb-4">
                Add gaming news through environment variables to see them here.
              </p>
              <div className="bg-purple-500/20 border border-purple-500/30 rounded-lg p-4 text-left">
                <p className="text-purple-300 text-sm font-mono">
                  NEWS_1=Game Title|Description|Category|Time|URL
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id={id} className={`py-16 px-4 sm:px-6 lg:px-8 ${className}`}>
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h3 className="text-3xl font-bold text-white">Latest Gaming News</h3>
          <button
            onClick={fetchNews}
            className="text-purple-400 hover:text-purple-300 text-sm transition-colors"
          >
            🔄 Refresh News
          </button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {news.map((article) => (
            <NewsCard key={article.id} article={article} />
          ))}
        </div>
        
        {news.length > 0 && (
          <div className="text-center mt-8">
            <p className="text-gray-400 text-sm">
              Showing {news.length} latest articles • Updated automatically
            </p>
          </div>
        )}
      </div>
    </section>
  );
}
