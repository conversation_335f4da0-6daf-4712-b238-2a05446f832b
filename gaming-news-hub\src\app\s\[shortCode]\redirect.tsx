'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface RedirectProps {
  shortCode: string;
}

export default function RedirectComponent({ shortCode }: RedirectProps) {
  const router = useRouter();

  useEffect(() => {
    // Check if we just came from quiz (check referrer or session storage)
    const fromQuiz = typeof window !== 'undefined' &&
      (document.referrer.includes('verify-challenge-x9k2m') ||
       sessionStorage.getItem(`justCompletedQuiz_${shortCode}`));

    if (fromQuiz) {
      console.log('Redirect component: Coming from quiz, going to final page');
      // Clear the flag
      if (typeof window !== 'undefined') {
        sessionStorage.removeItem(`justCompletedQuiz_${shortCode}`);
      }
      // Go back to final page with quiz parameter
      router.replace(`/s/${shortCode}?quiz=completed`);
    } else {
      console.log('Redirect component: Normal redirect to blog page');
      router.replace(`/s/${shortCode}/content-gateway-7h3p9`);
    }
  }, [router, shortCode]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-400 mx-auto mb-4"></div>
        <p className="text-white text-xl">Redirecting...</p>
      </div>
    </div>
  );
}
