import { extractThumbnail, getProductEmoji, calculateDiscount, ProductThumbnail } from './thumbnailExtractor';

export interface Deal {
  id: string;
  title: string;
  originalPrice: number;
  salePrice: number;
  productUrl: string;
  thumbnail: ProductThumbnail;
  emoji: string;
  discountPercentage: number;
}

// Parse deal string format: TITLE|ORIGINAL_PRICE|SALE_PRICE|PRODUCT_URL
function parseDealString(dealString: string, dealId: string): Omit<Deal, 'thumbnail'> | null {
  try {
    const parts = dealString.split('|');
    if (parts.length !== 4) {
      console.warn(`Invalid deal format for ${dealId}: ${dealString}`);
      return null;
    }

    const [title, originalPriceStr, salePriceStr, productUrl] = parts;
    const originalPrice = parseFloat(originalPriceStr);
    const salePrice = parseFloat(salePriceStr);

    if (isNaN(originalPrice) || isNaN(salePrice)) {
      console.warn(`Invalid prices for ${dealId}: ${originalPriceStr}, ${salePriceStr}`);
      return null;
    }

    if (salePrice >= originalPrice) {
      console.warn(`Sale price should be less than original price for ${dealId}`);
      return null;
    }

    return {
      id: dealId,
      title: title.trim(),
      originalPrice,
      salePrice,
      productUrl: productUrl.trim(),
      emoji: getProductEmoji(title),
      discountPercentage: calculateDiscount(originalPrice, salePrice)
    };
  } catch (error) {
    console.error(`Error parsing deal ${dealId}:`, error);
    return null;
  }
}

// Get all deals from environment variables
export async function getDealsFromEnv(): Promise<Deal[]> {
  const deals: Deal[] = [];
  
  // Look for environment variables starting with DEAL_
  const envVars = process.env;
  const dealKeys = Object.keys(envVars)
    .filter(key => key.startsWith('DEAL_'))
    .sort((a, b) => {
      // Sort by number: DEAL_1, DEAL_2, etc.
      const numA = parseInt(a.replace('DEAL_', ''));
      const numB = parseInt(b.replace('DEAL_', ''));
      return numA - numB;
    });

  for (const key of dealKeys) {
    const dealString = envVars[key];
    if (!dealString) continue;

    const parsedDeal = parseDealString(dealString, key);
    if (!parsedDeal) continue;

    try {
      // Extract thumbnail for the product
      const thumbnail = await extractThumbnail(parsedDeal.productUrl, parsedDeal.title);
      
      deals.push({
        ...parsedDeal,
        thumbnail
      });
    } catch (error) {
      console.error(`Error extracting thumbnail for ${key}:`, error);
      // Still add the deal with fallback thumbnail
      deals.push({
        ...parsedDeal,
        thumbnail: {
          url: '',
          alt: parsedDeal.title,
          fallback: 'from-gray-500 to-gray-600'
        }
      });
    }
  }

  return deals;
}

// Get deals for client-side rendering (cached version)
export function getStaticDeals(): Omit<Deal, 'thumbnail'>[] {
  const deals: Omit<Deal, 'thumbnail'>[] = [];
  
  // This runs at build time, so we can access process.env
  const envVars = process.env;
  const dealKeys = Object.keys(envVars)
    .filter(key => key.startsWith('DEAL_'))
    .sort((a, b) => {
      const numA = parseInt(a.replace('DEAL_', ''));
      const numB = parseInt(b.replace('DEAL_', ''));
      return numA - numB;
    });

  for (const key of dealKeys) {
    const dealString = envVars[key];
    if (!dealString) continue;

    const parsedDeal = parseDealString(dealString, key);
    if (parsedDeal) {
      deals.push(parsedDeal);
    }
  }

  return deals;
}
